<template>
	<view class="container">
		<!-- 页面标题 -->
		<view class="header">
			<text class="title">委托寄售</text>
		</view>
		<!-- 商品信息 -->
		<view class="order-info">
			<view class="info-row">
				<text class="label">商品名称：</text>
				<text class="value">{{ storeName }}</text>
			</view>
			<view class="info-row">
				<text class="label">原价：</text>
				<text class="value">¥ {{ price }}</text>
			</view>
			<view class="info-row">
				<text class="label">现价：</text>
				<text class="value">¥ {{ order.price }}</text>
			</view>
			<view class="info-row">
				<text class="label">数量：</text>
				<text class="value">1</text>
			</view>

			<view>
				<view>收款方式：</view>
				<view style="border: 1rpx solid #000; width: 650rpx;padding: 20rpx;margin-top: 20rpx;">
					<view>银行名称：{{bank.bankName}}</view>
					<view>银行卡号：{{bank.accountInfo }}</view>
				</view>
				<view @cllick="previewImg(1)">
					<view>微信收款码：</view>
					<view style="width: 200rpx;height:200rpx;">
						<img :src="chat.chatImg" @click="check" style="widtn:100%;height:100%;" alt="">
					</view>
				</view>
				<view @cllick="previewImg(2)">
					<view>支付宝收款码：</view>
					<view style="width: 200rpx;height:200rpx;">
						<img :src="alipay.alipayImg" @click="check2" style="widtn:100%;height:100%;" alt="">
					</view>
				</view>
			</view>
		</view>

		<!-- 手续费说明 -->
		<view class="fee-info">
			<text class="fee-text">根据《寄售规则》您需支付寄售价格的 3.5% 作为手续费：</text>
			<text class="fee-detail">委托价格:¥{{ order.price }}，需支付手续费:¥{{ order.moneyNum }}</text>
		</view>

		<!-- 支付方式 -->
		<view class="payment-options">
			<view class="option-row">
				<checkbox v-if="balance!=0" :checked="paymentOptions.balance" @click="useBalance" /> 可用余额：￥{{ balance }}
			</view>
			<view class="text" v-if="paymentOptions.balance">已抵扣：-￥ <text
					style="color: red;">{{ balance>=this.order.moneyNum?this.order.moneyNum:balance }}</text>
			</view>
			<view class="text" v-if="paymentOptions.balance">需付款：￥<text
					style="color: red;">{{this.order.moneyNum -balance<0?0:this.order.moneyNum -balance}}</text></view>
			<view class="option-row" v-if="flag!=2">
				<view class="left">
					付款凭证：
				</view>
				<view class="right">
					<u-upload :fileList="fileList" @afterRead="afterRead" @delete="deletePic" multiple></u-upload>
				</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="action">
			<button class="submit-btn" @click="submitOrder" :loading="submitLoading">确认发布</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				order: {},
				paymentOptions: {
					balance: false,
				},
				balance: '',
				userInfo: {},
				fileList: [],
				storeName: '',
				price: '',
				bank: {},
				chat: {},
				alipay: {},
				flag: 0,
				submitLoading:false,
			};
		},
		onLoad(options) {
			console.log(options.id);
			this.api_get(options.id)
			this.userInfo = uni.getStorageSync('userInfo')
			this.storeName = options.storename
			this.price = options.price
			this.api_getpay()
			this.getuserInfo()
		},
		methods: {
			getuserInfo() {
				this.$api.request({
					url: this.$api.getuserInfo + `/${this.userInfo.id}`
				}).then((res) => {
					this.balance = res.data.balance
				})
			},
			goBack() {
				uni.navigateBack();
			},
			api_get(id) {
				this.$api.request({
					url: this.$api.getseildetial + `/${id}`
				}).then((res) => {
					console.log(res, '数据');
					this.order = res.data
				})
			},

			api_getpay() {
				this.$api.request({
					url: this.$api.getpayways + '?userId=1'
				}).then((res) => {
					console.log(res.rows, '付款方式');
					this.bank = res.rows.filter(item => {
						return item.methodId == 1
					})[0]
					this.chat = res.rows.filter(item => {
						return item.methodId == 2
					})[0]
					this.alipay = res.rows.filter(item => {
						return item.methodId == 3
					})[0]
				})
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList.length
				let filePath
				lists.map((item) => {
					this.fileList.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					let result = await this.$api.uploadFile({
						url: this.$api.upPic,
						filePath: lists[i].url
					});
					result = JSON.parse(result);
					let item = this.fileList[fileListLen]
					this.fileList.splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result.url
					}))
					fileListLen++
				}
			},
			// previewImg(index) {
			// 	console.log(index, '图片被点击');
			// 	let photoImg = ''
			// 	if (index == 1) {
			// 		photoImg = this.chat.chatImg
			// 	} else {
			// 		photoImg = this.alipay.alipayImg
			// 	}
			// 	console.log('photoImg', photoImg)
			// 	let imgsArray = [];
			// 	imgsArray[0] = photoImg;
			// 	console.log('imgsArray[0]', imgsArray[0])
			// 	uni.previewImage({
			// 		current: 0,
			// 		urls: imgsArray,
			// 	});
			// },
			check() {
				let imgsArray = [];
				imgsArray[0] = this.chat.chatImg;
				console.log('imgsArray[0]', imgsArray[0])
				uni.previewImage({
					current: 0,
					urls: imgsArray,
				});
			},
			check2() {
				let imgsArray = [];
				imgsArray[0] = this.alipay.alipayImg;
				console.log('imgsArray[0]', imgsArray[0])
				uni.previewImage({
					current: 0,
					urls: imgsArray,
				});
			},
			// 删除图片
			deletePic(event) {
				this.fileList.splice(event.index, 1)
			},
			// 使用余额
			useBalance(e) {
				this.paymentOptions.balance = !this.paymentOptions.balance
				if (this.paymentOptions.balance == false) {
					this.flag = 0
				} else {
					if (this.balance >= this.order.moneyNum) {
						// this.order.moneyNum = this.order.moneyNum - this.balance
						this.flag = 2
					} else {
						this.flag = 1
					}
				}
			},
			submitOrder() {
				this.submitLoading=true;
				console.log("提交订单信息：", this.paymentOptions);
				if (this.paymentOptions.balance == false) {
					this.flag = 0
				} else {
					if (this.balance >= this.order.moneyNum) {
						// this.order.moneyNum = this.order.moneyNum - this.balance
						this.flag = 2
					} else {
						this.flag = 1
					}
				}
				uni.showModal({
					title: '操作通知',
					content: '是否确认发布?',
					success: (res) => {
						console.log(this.fileList, 'fileList');
						if (res.confirm) {
							if (this.flag != 2) {
								let imgs = ""
								this.fileList.forEach((item, index) => {
									imgs += `${item.url}` + ","
								})
								console.log(imgs, '修改的图片格式');
								if (this.fileList.length != 0) {
									this.$api.request({
										url: this.$api.putmessionon,
										method: "POST",
										data: {
											orderId: this.order.id,
											userId: uni.getStorageSync('userInfo').id,
											goodsId: this.order.goodsId, // 订单id
											price: this.order.moneyNum,
											uploadImg: imgs,
											type: this.flag,
										}
									}).then((res) => {
										if (res.code == 200) {
											uni.showToast({
												title: "发布成功",
												icon: "success"
											});
											setTimeout(() => {
												uni.navigateBack()
											}, 1500)
										}
									})
								} else {
									uni.showToast({
										title: "请上传支付凭证",
										icon: 'none'
									})
								}

							} else {
								this.$api.request({
									url: this.$api.putmessionon,
									method: "POST",
									data: {
										orderId: this.order.id,
										userId: uni.getStorageSync('userInfo').id,
										goodsId: this.order.goodsId, // 订单id
										price: this.order.moneyNum,
										// uploadImg: imgs,
										type: this.flag,
									}
								}).then((res) => {
									if (res.code == 200) {
										uni.showToast({
											title: "发布成功",
											icon: "success"
										});
										setTimeout(() => {
											uni.navigateBack()
										}, 1500)
									}
								})
							}
						}
					}
				})
			},

		},
	};
</script>

<style>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #3cb371;
		padding: 10rpx 20rpx;
		color: white;
	}

	.title {
		font-size: 32rpx;
	}

	.order-info,
	.fee-info,
	.payment-options {
		background-color: white;
		padding: 20rpx;
		margin-top: 20rpx;
		border-radius: 10rpx;
	}

	.info-row,
	.option-row {
		display: flex;
		justify-content: space-between;
		margin-top: 50rpx;
		margin-bottom: 10rpx;
	}

	.label {
		color: #555;
		font-size: 28rpx;
	}

	.value {
		color: #333;
		font-size: 28rpx;
	}

	.fee-text {
		color: #f00;
		font-size: 28rpx;
	}

	.fee-detail {
		color: #666;
		margin-top: 10rpx;
	}

	.submit-btn {
		width: 100%;
		background-color: #3cb371;
		color: white;
		padding: 15rpx;
		border-radius: 10rpx;
		font-size: 28rpx;
		margin-top: 20rpx;
	}

	.text {
		height: 60rpx;
		margin-top: 10rpx;
		line-height: 60rpx;
		padding-left: 30rpx;
		font-weight: bold;
	}
</style>